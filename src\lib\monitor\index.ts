import { generatorRole } from './generatorRole';
import { roomMemory } from './roomMemory';
import { tempScriptTask } from './tempTask';
import { flagMonitor } from './flagMemory';

export const gameMonitor = () => {
  tempScriptTask();
  flagMonitor();
  generatePixel();
};

export const roomMonitor = (room: Room) => {
  roomMemory(room);
  generatorRole(room);
};

// pixel
const generatePixel = () => {
  if (Game.cpu.bucket >= 10000) {
    const result = Game.cpu.generatePixel();

    if (result === OK) {
      console.log('生成 1 pixel', result);
    } else {
      console.log('生成 pixel 失败', result);
    }
  }
};
