export const flagMonitor = () => {
  for (const flagName in Game.flags) {
    const flag = Game.flags[flagName];
    const flagType = flag.memory.type;
    if (!flagType) continue;
    if (flagType === 'sourceRoom') {
      sourceRoomMonitor(flag);
    }
  }
};

const sourceRoomMonitor = (flag: CustomFlag<'sourceRoom'>) => {
  const { payload } = flag.memory;
  if (!payload) {
    const defaultPayload: Partial<RemoteSourceRoomPayload> = {
      harvesters: 1,
      haulers: 1,
      status: 'active',
    };
    flag.memory.payload = defaultPayload;
  } else {
    const { mainRoom } = payload;
    if (!mainRoom) return;
    const room = Game.rooms[mainRoom];
    if (!room) return;
    room.memory?.sourceRooms
      ? room.memory.sourceRooms.push(flag.pos.roomName)
      : (room.memory.sourceRooms = [flag.pos.roomName]);
  }
};
